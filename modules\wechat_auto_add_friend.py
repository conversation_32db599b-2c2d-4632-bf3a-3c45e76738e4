#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图像识别点击"添加到通讯录"按钮

作者: AI Assistant
创建时间: 2025-07-24
版本: 1.0.0

功能特性:
- 自动检测微信"添加朋友"窗口
- 图像识别"添加到通讯录"按钮
- 智能点击执行
- 完整的错误处理机制
- 详细的调试日志
"""

import time
import logging
import cv2
import numpy as np
import pyautogui
import pygetwindow as gw
from typing import Optional, Tuple, List
import os
from datetime import datetime

# 配置pyautogui
pyautogui.FAILSAFE = True  # 鼠标移到屏幕左上角时停止
pyautogui.PAUSE = 0.5      # 每次操作间隔0.5秒

class WeChatAutoAddFriend:
    """微信自动添加朋友类"""
    
    def __init__(self, debug_mode: bool = True):
        """
        初始化微信自动添加朋友实例
        
        Args:
            debug_mode: 是否开启调试模式
        """
        self.debug_mode = debug_mode
        self.setup_logging()
        self.screenshot_dir = "screenshots"
        self.ensure_screenshot_dir()
        
        # 微信窗口相关配置
        self.wechat_window_titles = [
            "微信",
            "WeChat",
            "添加朋友",
            "Add Friends",
            "添加联系人",
            "Add Contact"
        ]
        
        # 按钮识别相关配置
        self.button_texts = [
            "添加到通讯录",
            "Add to Contacts",
            "添加",
            "Add"
        ]
        
        self.logger.info("微信自动添加朋友脚本初始化完成")
    
    def setup_logging(self):
        """设置日志配置"""
        log_level = logging.DEBUG if self.debug_mode else logging.INFO
        
        # 创建日志格式
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # 设置控制台日志
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        
        # 设置文件日志
        file_handler = logging.FileHandler(
            f'wechat_auto_add_{datetime.now().strftime("%Y%m%d")}.log',
            encoding='utf-8'
        )
        file_handler.setFormatter(formatter)
        
        # 配置logger
        self.logger = logging.getLogger('WeChatAutoAdd')
        self.logger.setLevel(log_level)
        self.logger.addHandler(console_handler)
        self.logger.addHandler(file_handler)
    
    def ensure_screenshot_dir(self):
        """确保截图目录存在"""
        if not os.path.exists(self.screenshot_dir):
            os.makedirs(self.screenshot_dir)
            self.logger.info(f"创建截图目录: {self.screenshot_dir}")
    
    def find_wechat_windows(self) -> List[gw.Win32Window]:
        """
        查找所有微信相关窗口

        Returns:
            微信窗口列表
        """
        wechat_windows = []
        all_windows = gw.getAllWindows()

        for window in all_windows:
            if window.title and any(title in window.title for title in self.wechat_window_titles):
                # 兼容不同版本的pygetwindow库
                is_visible = self._check_window_visible(window)
                if is_visible and window.width > 100 and window.height > 100:
                    wechat_windows.append(window)
                    self.logger.debug(f"找到微信窗口: {window.title} - {window.width}x{window.height}")

        self.logger.info(f"共找到 {len(wechat_windows)} 个微信窗口")
        return wechat_windows

    def _check_window_visible(self, window) -> bool:
        """
        检查窗口是否可见（兼容不同版本的pygetwindow）

        Args:
            window: 窗口对象

        Returns:
            窗口是否可见
        """
        try:
            # 尝试使用 isVisible 属性
            if hasattr(window, 'isVisible'):
                return window.isVisible
            # 尝试使用 visible 属性
            elif hasattr(window, 'visible'):
                return window.visible
            # 如果都没有，假设窗口可见
            else:
                self.logger.warning(f"无法检查窗口可见性，假设窗口可见: {window.title}")
                return True
        except Exception as e:
            self.logger.warning(f"检查窗口可见性时发生错误: {e}")
            return True
    
    def find_add_friend_window(self) -> Optional[gw.Win32Window]:
        """
        查找"添加朋友"窗口
        
        Returns:
            添加朋友窗口对象，如果未找到返回None
        """
        wechat_windows = self.find_wechat_windows()
        
        # 优先查找包含"添加朋友"的窗口
        for window in wechat_windows:
            if "添加朋友" in window.title or "Add Friends" in window.title:
                self.logger.info(f"找到添加朋友窗口: {window.title}")
                return window
        
        # 如果没有找到专门的添加朋友窗口，查找主微信窗口
        for window in wechat_windows:
            if window.title == "微信" or window.title == "WeChat":
                self.logger.info(f"找到微信主窗口: {window.title}")
                return window
        
        self.logger.warning("未找到微信添加朋友窗口")
        return None

    def clean_screenshots_directory(self):
        """
        清理screenshots目录下的所有图片文件
        """
        try:
            if not os.path.exists(self.screenshot_dir):
                os.makedirs(self.screenshot_dir)
                self.logger.info(f"创建screenshots目录: {self.screenshot_dir}")
                return

            # 获取所有图片文件
            image_extensions = ['.png', '.jpg', '.jpeg', '.bmp', '.tiff']
            files_to_remove = []

            for file in os.listdir(self.screenshot_dir):
                file_path = os.path.join(self.screenshot_dir, file)
                if os.path.isfile(file_path):
                    _, ext = os.path.splitext(file.lower())
                    if ext in image_extensions:
                        files_to_remove.append(file_path)

            # 删除图片文件
            removed_count = 0
            for file_path in files_to_remove:
                try:
                    os.remove(file_path)
                    removed_count += 1
                except Exception as e:
                    self.logger.warning(f"删除文件失败 {file_path}: {e}")

            if removed_count > 0:
                self.logger.info(f"清理screenshots目录: 删除了 {removed_count} 个图片文件")
            else:
                self.logger.info("screenshots目录已经是干净的")

        except Exception as e:
            self.logger.error(f"清理screenshots目录失败: {e}")

    def verify_add_friend_window(self, screenshot: np.ndarray) -> bool:
        """
        验证截图是否为添加朋友窗口

        Args:
            screenshot: 窗口截图

        Returns:
            是否为添加朋友窗口
        """
        try:
            height, width = screenshot.shape[:2]

            # 检查截图尺寸是否合理
            if width < 200 or height < 300:
                self.logger.warning(f"窗口尺寸过小: {width}x{height}")
                return False

            if width > 2000 or height > 2000:
                self.logger.warning(f"窗口尺寸过大: {width}x{height}")
                return False

            # 检查是否有足够的内容
            gray = cv2.cvtColor(screenshot, cv2.COLOR_BGR2GRAY)

            # 计算非空白区域
            non_white_pixels = np.sum(gray < 240)
            total_pixels = width * height
            content_ratio = non_white_pixels / total_pixels

            if content_ratio < 0.1:
                self.logger.warning(f"窗口内容过少: {content_ratio:.3f}")
                return False

            # 检查是否有边缘（表示有UI元素）
            edges = cv2.Canny(gray, 50, 150)
            edge_pixels = np.sum(edges > 0)
            edge_ratio = edge_pixels / total_pixels

            if edge_ratio < 0.01:
                self.logger.warning(f"窗口边缘过少: {edge_ratio:.3f}")
                return False

            self.logger.info(f"窗口验证通过: 尺寸{width}x{height}, 内容比例{content_ratio:.3f}, 边缘比例{edge_ratio:.3f}")
            return True

        except Exception as e:
            self.logger.error(f"验证窗口失败: {e}")
            return False

    def verify_screenshot_content(self, screenshot: np.ndarray) -> bool:
        """
        验证截图内容是否合理

        Args:
            screenshot: 截图数组

        Returns:
            截图内容是否合理
        """
        try:
            height, width = screenshot.shape[:2]

            # 检查是否为空白或纯色图像
            gray = cv2.cvtColor(screenshot, cv2.COLOR_BGR2GRAY)

            # 计算图像的标准差，纯色图像标准差很小
            _, std_dev = cv2.meanStdDev(gray)
            std_dev = float(std_dev[0][0])
            if std_dev < 5:
                self.logger.warning(f"截图可能为纯色图像，标准差: {std_dev}")
                return False

            # 检查是否有足够的变化
            edges = cv2.Canny(gray, 50, 150)
            edge_count = np.sum(edges > 0)
            edge_ratio = edge_count / (width * height)

            if edge_ratio < 0.005:
                self.logger.warning(f"截图边缘过少，可能为空白图像: {edge_ratio}")
                return False

            # 检查颜色分布
            mean_color = np.mean(screenshot, axis=(0, 1))
            if np.all(mean_color > 240):  # 接近白色
                self.logger.warning("截图可能为白色背景")
                return False

            self.logger.debug(f"截图内容验证通过: 标准差{std_dev:.2f}, 边缘比例{edge_ratio:.4f}")
            return True

        except Exception as e:
            self.logger.error(f"验证截图内容失败: {e}")
            return False
    
    def capture_window_screenshot(self, window: gw.Win32Window) -> Optional[np.ndarray]:
        """
        截取指定窗口的截图（改进版，确保截取正确的添加朋友窗口）

        Args:
            window: 目标窗口

        Returns:
            截图的numpy数组，失败返回None
        """
        try:
            # 首先验证窗口是否为添加朋友窗口
            if not ("添加朋友" in window.title or "Add Friends" in window.title or "微信" in window.title):
                self.logger.warning(f"窗口标题不匹配: {window.title}")

            # 方法1: 尝试使用pygetwindow的方式
            try:
                # 激活并置顶窗口
                window.activate()
                time.sleep(0.5)  # 增加等待时间确保窗口激活

                # 获取窗口位置和大小
                left, top, width, height = window.left, window.top, window.width, window.height
                self.logger.info(f"目标窗口: {window.title}")
                self.logger.info(f"窗口位置: ({left}, {top}), 尺寸: {width}x{height}")

                # 验证窗口尺寸
                if width <= 0 or height <= 0:
                    raise ValueError(f"窗口尺寸无效: {width}x{height}")

                if width < 200 or height < 300:
                    self.logger.warning(f"窗口尺寸过小，可能不是添加朋友窗口: {width}x{height}")

                # 截取窗口区域
                screenshot = pyautogui.screenshot(region=(int(left), int(top), int(width), int(height)))

                # 转换为numpy数组
                screenshot_np = np.array(screenshot)
                screenshot_bgr = cv2.cvtColor(screenshot_np, cv2.COLOR_RGB2BGR)

                # 验证截图内容
                if not self.verify_screenshot_content(screenshot_bgr):
                    self.logger.warning("截图内容验证失败，可能截取了错误的窗口")

                # 保存调试截图
                if self.debug_mode:
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    debug_path = os.path.join(self.screenshot_dir, f"window_capture_method1_{timestamp}.png")
                    cv2.imwrite(debug_path, screenshot_bgr)
                    self.logger.debug(f"保存方法1截图: {debug_path}")

                return screenshot_bgr

            except Exception as e1:
                self.logger.warning(f"方法1截图失败: {e1}, 尝试方法2")

                # 方法2: 使用win32gui直接获取窗口句柄
                try:
                    import win32gui
                    import win32ui
                    import win32con
                    from PIL import Image
                except ImportError:
                    raise Exception("win32gui模块未安装，无法使用方法2")

                # 通过窗口标题重新查找窗口句柄，优先查找添加朋友窗口
                def enum_windows_callback(hwnd, windows):
                    if win32gui.IsWindowVisible(hwnd):
                        window_title = win32gui.GetWindowText(hwnd)
                        # 优先匹配添加朋友窗口
                        if "添加朋友" in window_title or "Add Friends" in window_title:
                            windows.insert(0, (hwnd, window_title))  # 插入到开头，优先级最高
                        elif window.title in window_title or window_title in window.title:
                            windows.append((hwnd, window_title))
                    return True

                windows = []
                win32gui.EnumWindows(enum_windows_callback, windows)

                if not windows:
                    raise Exception("无法找到窗口句柄")

                # 选择最匹配的窗口
                hwnd, matched_title = windows[0]
                self.logger.info(f"使用win32方法截取窗口: {matched_title}")

                # 获取窗口DC
                hwndDC = win32gui.GetWindowDC(hwnd)
                mfcDC = win32ui.CreateDCFromHandle(hwndDC)
                saveDC = mfcDC.CreateCompatibleDC()

                # 获取窗口尺寸
                left, top, right, bottom = win32gui.GetWindowRect(hwnd)
                width = right - left
                height = bottom - top

                self.logger.info(f"win32窗口尺寸: {width}x{height}, 位置: ({left}, {top})")

                # 验证窗口尺寸
                if width <= 0 or height <= 0:
                    raise Exception(f"win32窗口尺寸无效: {width}x{height}")

                # 创建bitmap
                saveBitMap = win32ui.CreateBitmap()
                saveBitMap.CreateCompatibleBitmap(mfcDC, width, height)
                saveDC.SelectObject(saveBitMap)

                # 复制窗口内容
                result = saveDC.BitBlt((0, 0), (width, height), mfcDC, (0, 0), win32con.SRCCOPY)
                if not result:
                    self.logger.warning("win32 BitBlt操作可能失败")

                # 转换为PIL图像
                bmpinfo = saveBitMap.GetInfo()
                bmpstr = saveBitMap.GetBitmapBits(True)
                im = Image.frombuffer('RGB', (bmpinfo['bmWidth'], bmpinfo['bmHeight']), bmpstr, 'raw', 'BGRX', 0, 1)

                # 清理资源
                win32gui.DeleteObject(saveBitMap.GetHandle())
                saveDC.DeleteDC()
                mfcDC.DeleteDC()
                win32gui.ReleaseDC(hwnd, hwndDC)

                # 转换为numpy数组
                screenshot_np = np.array(im)
                screenshot_bgr = cv2.cvtColor(screenshot_np, cv2.COLOR_RGB2BGR)

                # 验证截图内容
                if not self.verify_screenshot_content(screenshot_bgr):
                    self.logger.warning("win32截图内容验证失败")

                # 保存调试截图
                if self.debug_mode:
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    debug_path = os.path.join(self.screenshot_dir, f"window_capture_win32_{timestamp}.png")
                    cv2.imwrite(debug_path, screenshot_bgr)
                    self.logger.info(f"保存win32截图: {debug_path}")

                self.logger.info("win32截图成功")
                return screenshot_bgr

        except Exception as e:
            self.logger.error(f"截取窗口截图失败: {e}")

            # 方法3: 备选方案 - 截取整个屏幕
            try:
                self.logger.info("尝试备选方案：截取整个屏幕")
                screenshot = pyautogui.screenshot()
                screenshot_np = np.array(screenshot)
                screenshot_bgr = cv2.cvtColor(screenshot_np, cv2.COLOR_RGB2BGR)

                if self.debug_mode:
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    debug_path = os.path.join(self.screenshot_dir, f"fullscreen_capture_{timestamp}.png")
                    cv2.imwrite(debug_path, screenshot_bgr)
                    self.logger.debug(f"保存全屏截图: {debug_path}")

                return screenshot_bgr

            except Exception as e3:
                self.logger.error(f"备选截图方案也失败: {e3}")
                return None
            
    def _is_green_button(self, roi: np.ndarray) -> bool:
        """
        检查ROI区域是否为绿色按钮

        Args:
            roi: 待检查的图像区域

        Returns:
            是否为绿色按钮
        """
        if roi.size == 0:
            return False

        try:
            # 转换为HSV颜色空间以便更好地检测绿色
            if len(roi.shape) == 3:
                hsv = cv2.cvtColor(roi, cv2.COLOR_BGR2HSV)
            else:
                # 如果是灰度图，转换为BGR再转HSV
                bgr = cv2.cvtColor(roi, cv2.COLOR_GRAY2BGR)
                hsv = cv2.cvtColor(bgr, cv2.COLOR_BGR2HSV)

            # 定义绿色的HSV范围（微信绿色）
            # 微信的绿色大约是 HSV(120, 100, 80) 左右
            lower_green = np.array([40, 40, 40])   # 较宽的绿色范围下限
            upper_green = np.array([80, 255, 255]) # 较宽的绿色范围上限

            # 创建绿色掩码
            green_mask = cv2.inRange(hsv, lower_green, upper_green)

            # 计算绿色像素的比例
            green_pixels = np.sum(green_mask > 0)
            total_pixels = roi.shape[0] * roi.shape[1]
            green_ratio = green_pixels / total_pixels

            # 如果绿色像素超过30%，认为是绿色按钮
            is_green = green_ratio > 0.3

            if is_green:
                self.logger.debug(f"检测到绿色按钮: 绿色像素比例 {green_ratio:.2f}")

            return is_green

        except Exception as e:
            self.logger.debug(f"绿色按钮检测失败: {e}")
            # 备选方案：检查平均颜色是否偏绿
            try:
                if len(roi.shape) == 3:
                    # BGR格式，绿色通道是第二个
                    mean_color = np.mean(roi, axis=(0, 1))
                    # 如果绿色通道明显高于其他通道，可能是绿色按钮
                    if len(mean_color) >= 3 and mean_color[1] > mean_color[0] + 20 and mean_color[1] > mean_color[2] + 20:
                        self.logger.debug(f"备选方案检测到绿色按钮: BGR均值 {mean_color}")
                        return True
            except:
                pass
            return False







    def find_button_by_image_processing(self, screenshot: np.ndarray) -> Optional[Tuple[int, int, bool]]:
        """
        使用改进的图像处理技术查找"添加到通讯录"按钮

        Args:
            screenshot: 窗口截图

        Returns:
            (按钮中心坐标x, 按钮中心坐标y, 是否需要文字验证)，未找到返回None
        """
        try:
            self.logger.info("使用改进的图像处理方法查找按钮")

            # 转换为灰度图
            gray = cv2.cvtColor(screenshot, cv2.COLOR_BGR2GRAY)
            height, width = gray.shape

            self.logger.info(f"截图尺寸: {width}x{height}")

            # 首先尝试在底部区域查找真正的按钮
            button_result = self._find_button_in_bottom_area(screenshot, gray)
            if button_result:
                button_pos, needs_verification = button_result
                return (button_pos[0], button_pos[1], needs_verification)

            # 如果底部没找到按钮，返回None
            self.logger.warning("底部区域未找到按钮")

            return None

        except Exception as e:
            self.logger.error(f"图像处理查找按钮失败: {e}")

        return None

    def _find_button_in_bottom_area(self, screenshot: np.ndarray, gray: np.ndarray) -> Optional[Tuple[Tuple[int, int], bool]]:
        """
        在底部245像素区域查找"添加到通讯录"按钮（优化版）

        Args:
            screenshot: 原始截图
            gray: 灰度图

        Returns:
            按钮中心坐标，未找到返回None
        """
        height, _ = gray.shape

        # 优化：重点搜索底部245像素区域
        bottom_region_height = min(245, height)
        bottom_start = height - bottom_region_height
        bottom_region = gray[bottom_start:, :]

        self.logger.info(f"搜索底部245像素区域: Y={bottom_start}-{height} (高度:{bottom_region_height})")

        # 特别关注距离底部154像素的位置（Y坐标范围130-160）
        special_y_global = height - 154
        special_y_range = 30  # ±30像素范围
        special_y_start = max(bottom_start, special_y_global - special_y_range)
        special_y_end = min(height, special_y_global + special_y_range)

        if special_y_start < special_y_end:
            self.logger.info(f"特别关注区域: Y={special_y_start}-{special_y_end} (距底部154像素)")

        # 使用优化的边缘检测参数，专门适配微信界面的灰色按钮文字
        edges = cv2.Canny(bottom_region, 15, 60)  # 进一步降低阈值以检测灰色文字
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        # 保存底部区域边缘检测调试图像
        if self.debug_mode:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            bottom_edge_debug_path = os.path.join(self.screenshot_dir, f"bottom_edge_detection_{timestamp}.png")
            cv2.imwrite(bottom_edge_debug_path, edges)
            self.logger.debug(f"保存底部边缘检测调试图像: {bottom_edge_debug_path}")

        self.logger.info(f"底部区域原始检测到 {len(contours)} 个轮廓")

        button_candidates = []

        for contour in contours:
            x, y, w, h = cv2.boundingRect(contour)
            # 调整y坐标到全图坐标系
            y_global = y + bottom_start
            area = w * h
            aspect_ratio = w / h if h > 0 else 0

            # 优化的按钮特征检测，专门针对"添加到通讯录"按钮：
            # 1. 宽度30-130像素（适应不同分辨率下中文5字按钮的实际宽度变化）
            # 2. 高度15-40像素（适合中文字符高度）
            # 3. 长宽比3-8（横向文字按钮）
            # 4. 面积750-6000（合理的文字按钮面积）
            # 5. 文字实际长度70-95像素（"添加到通讯录"5个中文字符的实际像素长度）

            # 检查是否为已知的"添加到通讯录"按钮特征
            is_known_button = (
                90 <= w <= 140 and 25 <= h <= 40 and  # 接近已知尺寸 131x33
                special_y_start <= y_global <= special_y_end and  # 在特殊位置
                3.0 <= aspect_ratio <= 5.0  # 接近已知长宽比 3.97
            )

            # 只记录重要的轮廓信息
            if is_known_button or (90 <= w <= 140 and 25 <= h <= 40):
                self.logger.info(f"重要轮廓: 位置({x},{y_global}), 尺寸{w}x{h}, 长宽比{aspect_ratio:.2f}, 已知特征:{is_known_button}")
            else:
                self.logger.debug(f"底部轮廓: 位置({x},{y_global}), 尺寸{w}x{h}, 长宽比{aspect_ratio:.2f}, 面积{area}")

            # 基本筛选：排除明显不是文字的区域
            basic_filter = (10 <= w <= 200 and 8 <= h <= 60 and  # 放宽基本尺寸
                          1.0 <= aspect_ratio <= 20 and 80 <= area <= 12000)  # 放宽长宽比和面积

            if basic_filter:

                roi = gray[y_global:y_global+h, x:x+w]
                mean_intensity = np.mean(roi)
                std_intensity = np.std(roi)

                # 计算位置得分，特殊位置得分更高
                position_score = 1.0
                if special_y_start <= y_global <= special_y_end:
                    position_score = 3.0  # 特殊位置最高分
                    self.logger.info(f"发现特殊位置按钮候选: Y={y_global} (距底部154像素区域)")
                elif y_global >= bottom_start + bottom_region_height * 0.5:
                    position_score = 2.0  # 底部下半区域高分

                # 检查是否符合"添加到通讯录"的精确特征
                target_size_ok = (30 <= w <= 130 and 15 <= h <= 40 and
                                3 <= aspect_ratio <= 8 and 750 <= area <= 6000)
                text_length_ok = (70 <= w <= 95)
                is_target_candidate = target_size_ok and text_length_ok

                # 检查是否为绿色按钮（"添加到通讯录"按钮通常是绿色的）
                is_green_button = self._is_green_button(roi)

                # 对于特殊位置、绿色按钮或已知按钮特征，进一步放宽颜色要求
                if special_y_start <= y_global <= special_y_end or is_green_button or is_known_button:
                    color_threshold = 100  # 特殊情况使用非常宽松的阈值
                    self.logger.debug(f"特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: {color_threshold}")
                else:
                    color_threshold = 60  # 普通位置使用稍微宽松的阈值

                color_ok = std_intensity < color_threshold

                if not color_ok:
                    self.logger.debug(f"颜色变化过大，跳过: std={std_intensity:.1f} (阈值:{color_threshold})")
                    continue

                if color_ok:
                    # 为特殊情况增加额外得分
                    if is_green_button:
                        position_score += 1.0  # 绿色按钮额外加分
                        self.logger.info(f"发现绿色按钮候选: 位置({x},{y_global}), 尺寸{w}x{h}")

                    if is_known_button:
                        position_score += 2.0  # 已知按钮特征额外加分更多
                        self.logger.info(f"发现已知按钮特征候选: 位置({x},{y_global}), 尺寸{w}x{h}")

                    button_candidates.append({
                        'x': x, 'y': y_global, 'w': w, 'h': h,
                        'area': area, 'aspect_ratio': aspect_ratio,
                        'mean_intensity': mean_intensity,
                        'std_intensity': std_intensity,
                        'position_score': position_score,
                        'type': 'bottom_button',
                        'in_special_region': special_y_start <= y_global <= special_y_end,
                        'is_target_candidate': is_target_candidate,
                        'target_size_ok': target_size_ok,
                        'text_length_ok': text_length_ok,
                        'is_green_button': is_green_button,
                        'is_known_button': is_known_button
                    })

                    if is_target_candidate:
                        self.logger.info(f"发现目标候选: 位置({x},{y_global}), 尺寸{w}x{h}")
                    elif is_known_button:
                        self.logger.info(f"发现已知按钮特征: 位置({x},{y_global}), 尺寸{w}x{h}")
                    elif target_size_ok:
                        self.logger.debug(f"尺寸合格但文字长度不符: 宽度{w} (需要70-95)")
                    elif is_green_button:
                        self.logger.info(f"发现绿色按钮但尺寸不完全匹配: 位置({x},{y_global}), 尺寸{w}x{h}")

        if button_candidates:
            self.logger.info(f"底部区域找到 {len(button_candidates)} 个按钮候选")

            # 优先选择目标候选（符合"添加到通讯录"特征的）
            target_candidates = [c for c in button_candidates if c.get('is_target_candidate', False)]
            known_candidates = [c for c in button_candidates if c.get('is_known_button', False)]
            green_candidates = [c for c in button_candidates if c.get('is_green_button', False)]
            special_candidates = [c for c in button_candidates if c['in_special_region']]

            if target_candidates:
                # 优先选择目标候选中位置得分最高的
                selected = max(target_candidates, key=lambda x: (x['position_score'], x['area']))
                self.logger.info(f"选择目标候选按钮: Y={selected['y']}, 符合'添加到通讯录'特征")
            elif known_candidates:
                # 其次选择已知按钮特征（最有可能是"添加到通讯录"按钮）
                selected = max(known_candidates, key=lambda x: (x['position_score'], x['area']))
                self.logger.info(f"选择已知按钮特征: Y={selected['y']}, 很可能是'添加到通讯录'按钮")
            elif green_candidates:
                # 再次选择绿色按钮（很可能是"添加到通讯录"按钮）
                selected = max(green_candidates, key=lambda x: (x['position_score'], x['area']))
                self.logger.info(f"选择绿色按钮: Y={selected['y']}, 可能是'添加到通讯录'按钮")
            elif special_candidates:
                # 再次选择特殊位置的按钮
                selected = max(special_candidates, key=lambda x: (x['position_score'], x['area']))
                self.logger.info(f"选择特殊位置按钮: Y={selected['y']} (距底部154像素)")
            else:
                # 最后选择位置得分和面积最高的
                selected = max(button_candidates, key=lambda x: (x['position_score'], x['area']))
                self.logger.info(f"选择最佳候选按钮: Y={selected['y']}")

            center_x = selected['x'] + selected['w'] // 2
            center_y = selected['y'] + selected['h'] // 2

            self.logger.info(f"在底部找到按钮: ({center_x}, {center_y}), "
                           f"尺寸: {selected['w']}x{selected['h']}, "
                           f"位置得分: {selected['position_score']}, "
                           f"目标候选: {selected.get('is_target_candidate', False)}, "
                           f"绿色按钮: {selected.get('is_green_button', False)}, "
                           f"特殊位置: {selected.get('in_special_region', False)}")

            # 判断是否需要文字验证
            # 如果是目标候选、已知按钮特征或绿色按钮，则不需要验证
            needs_verification = not (
                selected.get('is_target_candidate', False) or
                selected.get('is_known_button', False) or
                selected.get('is_green_button', False)
            )

            if not needs_verification:
                self.logger.info("检测到高置信度按钮，跳过文字验证")
            else:
                self.logger.info("需要进行文字验证")

            self._save_debug_image(screenshot, [selected], "bottom_button")
            return ((center_x, center_y), needs_verification)

        self.logger.warning("底部245像素区域未找到符合条件的按钮")
        return None







    def _save_debug_image(self, screenshot: np.ndarray, candidates: list, search_type: str):
        """
        保存调试图像

        Args:
            screenshot: 原始截图
            candidates: 候选区域列表
            search_type: 搜索类型
        """
        if not self.debug_mode or not candidates:
            return

        debug_img = screenshot.copy()

        # 不同搜索类型使用不同颜色
        color_map = {
            'bottom_button': (0, 255, 0),    # 绿色
            'text_link': (255, 0, 0),        # 蓝色
            'general_search': (0, 255, 255)  # 黄色
        }

        color = color_map.get(search_type, (128, 128, 128))

        # 标记选中的区域（粗线）
        if candidates:
            selected = candidates[0]
            x, y, w, h = selected['x'], selected['y'], selected['w'], selected['h']
            cv2.rectangle(debug_img, (x, y), (x + w, y + h), color, 3)
            cv2.circle(debug_img, (x + w//2, y + h//2), 5, (0, 0, 255), -1)
            cv2.putText(debug_img, f"SELECTED-{search_type}", (x, y-10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)

        # 标记其他候选区域（细线）
        for i, candidate in enumerate(candidates[1:6]):
            x, y, w, h = candidate['x'], candidate['y'], candidate['w'], candidate['h']
            cv2.rectangle(debug_img, (x, y), (x + w, y + h), color, 1)
            cv2.putText(debug_img, f"{i+2}", (x, y-5), cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)

        # 保存图像
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        debug_path = os.path.join(self.screenshot_dir, f"improved_detection_{search_type}_{timestamp}.png")
        cv2.imwrite(debug_path, debug_img)

        self.logger.debug(f"保存改进的检测调试图片: {debug_path}")



    def click_button(self, window: gw.Win32Window, button_pos: Tuple[int, int]) -> bool:
        """
        点击按钮

        Args:
            window: 窗口对象
            button_pos: 按钮在窗口中的相对坐标

        Returns:
            点击是否成功
        """
        try:
            # 确保窗口激活
            window.activate()
            time.sleep(0.3)

            # 计算屏幕绝对坐标
            screen_x = window.left + button_pos[0]
            screen_y = window.top + button_pos[1]

            self.logger.info(f"准备点击按钮: 窗口坐标({button_pos[0]}, {button_pos[1]}) -> 屏幕坐标({screen_x}, {screen_y})")

            # 移动鼠标到目标位置
            pyautogui.moveTo(screen_x, screen_y, duration=0.3)
            time.sleep(0.2)

            # 执行点击
            pyautogui.click(screen_x, screen_y)

            self.logger.info("按钮点击完成")

            # 🆕 立即检测频率错误
            self.logger.info("[SEARCH] 检测是否出现频率错误对话框...")
            frequency_error_result = self._handle_frequency_error_after_click()

            if frequency_error_result.get("handled_error", False):
                self.logger.warning("[WARNING] 检测到并处理了频率错误")
                # 即使检测到频率错误，也返回True，让上层逻辑处理重启
                return True

            return True

        except Exception as e:
            self.logger.error(f"点击按钮失败: {e}")
            return False

    def _handle_frequency_error_after_click(self) -> dict:
        """在点击添加到通讯录按钮后处理频率错误

        Returns:
            dict: 处理结果
        """
        try:
            # 导入频率错误处理器
            from .frequency_error_handler import FrequencyErrorHandler

            # 创建错误处理器
            error_handler = FrequencyErrorHandler(self.logger)

            # 检测频率错误
            self.logger.info("[SEARCH] 检测是否出现'操作过于频繁'错误...")
            detection_result = error_handler.detect_frequency_error_after_click(timeout=3.0)

            if detection_result.has_error:
                self.logger.warning(f"[WARNING] 检测到频率错误: {detection_result.error_type}")
                self.logger.warning(f"[NOTE] 错误信息: {detection_result.error_message}")

                # 🆕 立即点击错误对话框的确定按钮
                self.logger.info("[START] 立即处理频率错误对话框...")
                click_success = error_handler._click_error_dialog_ok_button(detection_result)

                if click_success:
                    self.logger.info("[OK] 频率错误对话框确定按钮点击成功")
                else:
                    self.logger.warning("[WARNING] 频率错误对话框确定按钮点击失败，但继续处理")

                # 🆕 然后执行完整的频率错误处理流程
                self.logger.info("[RETRY] 执行完整的频率错误处理流程...")
                handle_success = error_handler.handle_frequency_error(detection_result)

                if handle_success:
                    self.logger.info("[OK] 频率错误处理完成")

                    # 🆕 确保设置重新开始标志
                    try:
                        error_handler._set_restart_flag()
                        self.logger.info("[RETRY] 已设置重新开始标志")
                    except Exception as e:
                        self.logger.warning(f"[WARNING] 设置重新开始标志失败: {e}")

                    return {
                        "handled_error": True,
                        "error_type": detection_result.error_type,
                        "success": True,
                        "immediate_click": True
                    }
                else:
                    # 如果处理失败，仍然返回已处理状态（因为至少点击了确定按钮）
                    self.logger.warning("[WARNING] 频率错误完整处理失败，但已点击确定按钮")

                    return {
                        "handled_error": True,
                        "error_type": detection_result.error_type,
                        "success": False,
                        "immediate_click": True
                    }
            else:
                self.logger.info("[OK] 未检测到频率错误，继续正常流程")
                return {
                    "handled_error": False,
                    "error_type": "none",
                    "success": True,
                    "immediate_click": False
                }

        except Exception as e:
            self.logger.error(f"[ERROR] 频率错误处理异常: {e}")
            return {
                "handled_error": False,
                "error_type": "detection_error",
                "success": False,
                "error_message": str(e),
                "immediate_click": False
            }

    def execute_auto_add_friend(self) -> bool:
        """
        执行自动添加朋友操作

        Returns:
            操作是否成功
        """
        self.logger.info("开始执行自动添加朋友操作")

        try:
            # 1. 查找微信添加朋友窗口
            window = self.find_add_friend_window()
            if not window:
                self.logger.error("未找到微信添加朋友窗口")
                return False

            # 2. 截取窗口截图
            screenshot = self.capture_window_screenshot(window)
            if screenshot is None:
                self.logger.error("截取窗口截图失败")
                return False

            button_pos = None
            text_verified = False  # 添加文字验证标志







            # 3. 使用图像处理方法查找按钮（主要方法）
            if not button_pos:
                self.logger.info("尝试使用图像处理方法查找按钮")
                image_result = self.find_button_by_image_processing(screenshot)
                if image_result:
                    button_x, button_y, needs_verification = image_result
                    button_pos = (button_x, button_y)

                    if needs_verification:
                        # 图像处理方法找到按钮后，需要进行文字验证
                        self.logger.warning("图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字")
                        text_verified = self.verify_button_text(screenshot, button_pos)
                        if not text_verified:
                            self.logger.warning("图像处理找到的按钮未包含'添加到通讯录'文字，放弃点击")
                            button_pos = None
                    else:
                        # 高置信度按钮，跳过验证
                        self.logger.info("检测到高置信度按钮（已知特征/绿色按钮），跳过文字验证")
                        text_verified = True

            # 4. 如果都失败了或文字验证失败，返回错误
            if not button_pos or not text_verified:
                self.logger.error("无法找到'添加到通讯录'按钮，结束当前流程")
                return False

            # 5. 执行点击操作
            self.logger.info("文字验证通过，准备执行点击操作")
            success = self.click_button(window, button_pos)

            if success:
                self.logger.info("自动添加朋友操作执行成功")
            else:
                self.logger.error("自动添加朋友操作执行失败")

            return success

        except Exception as e:
            self.logger.error(f"执行自动添加朋友操作时发生异常: {e}")
            return False

    def stop(self):
        """
        停止自动添加朋友操作

        注意：当前实现为占位符方法，用于接口一致性
        实际的停止逻辑可以在此处添加
        """
        self.logger.info("[STOP] 停止自动添加朋友操作")
        # 这里可以添加实际的停止逻辑，比如：
        # - 设置停止标志
        # - 清理资源
        # - 中断正在进行的操作等

    def cleanup(self):
        """
        清理资源

        清理截图目录、日志资源等
        """
        try:
            self.logger.info("🧹 开始清理WeChatAutoAddFriend资源...")

            # 清理截图目录
            if hasattr(self, 'clean_screenshots_directory'):
                self.clean_screenshots_directory()

            self.logger.info("[OK] WeChatAutoAddFriend资源清理完成")

        except Exception as e:
            self.logger.error(f"[ERROR] WeChatAutoAddFriend资源清理异常: {e}")

    def verify_button_text(self, screenshot: np.ndarray, button_pos: Tuple[int, int],
                          region_size: int = 50) -> bool:
        """
        验证按钮区域是否包含"添加到通讯录"文字

        Args:
            screenshot: 窗口截图
            button_pos: 按钮中心坐标
            region_size: 验证区域的半径大小

        Returns:
            是否包含目标文字
        """
        try:
            self.logger.info(f"开始验证按钮区域({button_pos[0]}, {button_pos[1]})是否包含'添加到通讯录'文字")

            height, width = screenshot.shape[:2]
            center_x, center_y = button_pos

            # 计算验证区域的边界
            left = max(0, center_x - region_size)
            right = min(width, center_x + region_size)
            top = max(0, center_y - region_size)
            bottom = min(height, center_y + region_size)

            # 提取按钮区域
            button_region = screenshot[top:bottom, left:right]

            if button_region.size == 0:
                self.logger.warning("按钮区域为空")
                return False

            # 保存验证区域的调试图像
            if self.debug_mode:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                debug_path = os.path.join(self.screenshot_dir, f"button_verification_region_{timestamp}.png")
                cv2.imwrite(debug_path, button_region)
                self.logger.debug(f"保存按钮验证区域图像: {debug_path}")

            # 尝试使用OCR验证文字
            try:
                import pytesseract

                # 配置OCR参数
                custom_config = r'--oem 3 --psm 8 -l chi_sim+eng'

                # 执行OCR识别
                text = pytesseract.image_to_string(button_region, config=custom_config).strip()

                self.logger.info(f"按钮区域OCR识别结果: '{text}'")

                # 检查是否包含目标文字
                target_texts = ["添加到通讯录", "添加", "Add to Contacts", "Add"]
                for target_text in target_texts:
                    if target_text in text or text in target_text:
                        self.logger.info(f"验证成功: 找到目标文字 '{target_text}' 在识别结果 '{text}' 中")
                        return True

                self.logger.warning(f"验证失败: 未在识别结果 '{text}' 中找到目标文字")
                return False

            except ImportError:
                self.logger.warning("pytesseract未安装，无法进行OCR验证")
                # 如果没有OCR，使用图像特征验证
                return self._verify_button_by_image_features(button_region)
            except Exception as e:
                self.logger.warning(f"OCR验证失败: {e}，使用图像特征验证")
                # OCR失败时，使用图像特征验证作为备选
                return self._verify_button_by_image_features(button_region)

        except Exception as e:
            self.logger.error(f"验证按钮文字失败: {e}")
            return False

    def _verify_button_by_image_features(self, button_region: np.ndarray) -> bool:
        """
        通过图像特征验证按钮（当OCR不可用时的备选方案）

        Args:
            button_region: 按钮区域图像

        Returns:
            是否可能包含目标文字
        """
        try:
            # 转换为灰度图
            if len(button_region.shape) == 3:
                gray = cv2.cvtColor(button_region, cv2.COLOR_BGR2GRAY)
            else:
                gray = button_region.copy()

            height, width = gray.shape

            # 检查区域是否有足够的文字特征
            edges = cv2.Canny(gray, 50, 150)
            edge_count = np.sum(edges > 0)
            edge_ratio = edge_count / (width * height)

            # 检查亮度分布
            mean_val, std_val = cv2.meanStdDev(gray)
            mean_intensity = float(mean_val[0][0])
            std_intensity = float(std_val[0][0])

            # 基于经验的特征判断（放宽条件以适应不同的按钮样式）
            # 1. 边缘密度适中（有文字但不是纯边缘）
            # 2. 亮度分布合理（不是纯白或纯黑）
            # 3. 亮度变化适中（有文字轮廓）

            has_text_features = (
                0.01 <= edge_ratio <= 0.5 and  # 放宽边缘密度范围
                30 <= mean_intensity <= 250 and  # 放宽亮度范围，允许更亮的按钮
                5 <= std_intensity <= 100  # 放宽亮度变化范围
            )

            self.logger.info(f"图像特征验证: 边缘比例={edge_ratio:.4f}, "
                           f"平均亮度={mean_intensity:.1f}, 亮度标准差={std_intensity:.1f}")

            if has_text_features:
                self.logger.info("图像特征验证通过: 区域可能包含文字")
                return True
            else:
                self.logger.warning("图像特征验证失败: 区域不太可能包含目标文字")
                return False

        except Exception as e:
            self.logger.error(f"图像特征验证失败: {e}")
            return False


def main():
    """主函数 - 全自动化运行"""
    print("=" * 60)
    print("微信自动添加朋友脚本 - 全自动化版本")
    print("=" * 60)

    # 创建自动化实例
    auto_add = WeChatAutoAddFriend(debug_mode=True)

    # 1. 清理screenshots目录
    print("🧹 清理screenshots目录...")
    auto_add.clean_screenshots_directory()

    # 2. 自动检测微信窗口
    print("[SEARCH] 自动检测微信窗口...")
    window = auto_add.find_add_friend_window()

    if not window:
        print("[ERROR] 未找到微信添加朋友窗口!")
        print("请确保:")
        print("1. 微信已经打开")
        print("2. 添加朋友窗口已经显示")
        return False

    print(f"[OK] 找到微信窗口: {window.title} ({window.width}x{window.height})")

    # 3. 验证窗口内容
    print("📸 验证窗口内容...")
    screenshot = auto_add.capture_window_screenshot(window)

    if screenshot is None:
        print("[ERROR] 无法截取窗口截图!")
        return False

    height, width = screenshot.shape[:2]
    print(f"[OK] 成功截取窗口截图: {width}x{height}")

    # 4. 验证是否为添加朋友窗口
    if not auto_add.verify_add_friend_window(screenshot):
        print("[WARNING] 警告: 当前窗口可能不是添加朋友窗口")
        print("继续尝试执行...")

    print("\n[START] 开始自动执行...")

    # 5. 执行自动添加朋友操作
    success = auto_add.execute_auto_add_friend()

    if success:
        print("\n[OK] 自动添加朋友操作成功完成!")
        print("📁 调试图像已保存到screenshots目录")
    else:
        print("\n[ERROR] 自动添加朋友操作失败!")
        print("📁 请检查screenshots目录中的调试图像")
        print("📄 请检查日志文件获取详细错误信息")

    return success


if __name__ == "__main__":
    main()
