#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一日志去重管理器
解决微信自动化项目中的日志重复问题

功能特性：
1. 全局日志去重机制
2. 智能重复检测
3. 时间窗口控制
4. 统计信息收集
5. 性能优化

作者：AI助手
创建时间：2025-08-02
版本：v1.0
"""

import logging
import time
import hashlib
import threading
from typing import Dict, List, Optional, Tuple, Any
from collections import defaultdict, deque
from dataclasses import dataclass
from datetime import datetime, timedelta


@dataclass
class LogEntry:
    """日志条目数据结构"""
    timestamp: float
    level: str
    message: str
    module: str
    core_hash: str
    count: int = 1


@dataclass
class DeduplicationStats:
    """去重统计信息"""
    total_logs: int = 0
    duplicated_logs: int = 0
    suppressed_logs: int = 0
    unique_logs: int = 0
    duplicate_ratio: float = 0.0


class LogDeduplicationManager:
    """统一日志去重管理器"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        """单例模式实现"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        """初始化去重管理器"""
        if hasattr(self, '_initialized'):
            return
            
        self._initialized = True
        
        # 配置参数
        self.suppress_duration = 30  # 重复日志抑制时间（秒）
        self.max_recent_logs = 1000  # 最大保留日志数量
        self.cleanup_interval = 300  # 清理间隔（秒）
        self.hash_length = 16  # 哈希长度
        
        # 数据存储
        self.log_cache: Dict[str, LogEntry] = {}  # 日志缓存
        self.recent_logs: deque = deque(maxlen=self.max_recent_logs)  # 最近日志
        self.suppressed_counts: Dict[str, int] = defaultdict(int)  # 抑制计数
        self.last_cleanup = time.time()  # 上次清理时间
        
        # 统计信息
        self.stats = DeduplicationStats()
        
        # 线程锁
        self.cache_lock = threading.Lock()
        
        print(f"✅ 日志去重管理器初始化完成 - 抑制时间: {self.suppress_duration}s")
    
    def extract_log_core(self, message: str) -> str:
        """提取日志消息的核心内容，去除变量部分"""
        try:
            import re
            
            # 去除时间戳
            core = re.sub(r'\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}[,\.]\d{3}', '', message)
            
            # 去除数字变量
            core = re.sub(r'第 \d+ 个', '第 X 个', core)
            core = re.sub(r'\d+/\d+', 'X/Y', core)
            core = re.sub(r'句柄: \d+', '句柄: X', core)
            core = re.sub(r'窗口 \d+', '窗口 X', core)
            core = re.sub(r'\d{2}:\d{2}:\d{2}', 'HH:MM:SS', core)
            core = re.sub(r'\d+\.\d+', 'X.X', core)
            core = re.sub(r'\(\d+x\d+\)', '(XxY)', core)
            
            # 去除多余空格
            core = ' '.join(core.split())
            
            return core.strip()
            
        except Exception:
            return message
    
    def generate_log_hash(self, level: str, core_message: str, module: str) -> str:
        """生成日志哈希值"""
        try:
            content = f"{level}:{module}:{core_message}"
            hash_obj = hashlib.md5(content.encode('utf-8'))
            return hash_obj.hexdigest()[:self.hash_length]
        except Exception:
            return f"{level}_{module}_{len(core_message)}"
    
    def should_log_message(self, level: str, message: str, module: str = "unknown") -> Tuple[bool, Optional[str]]:
        """判断是否应该记录日志消息
        
        Returns:
            Tuple[bool, Optional[str]]: (是否应该记录, 附加信息)
        """
        current_time = time.time()
        
        # 定期清理过期数据
        if current_time - self.last_cleanup > self.cleanup_interval:
            self._cleanup_expired_logs(current_time)
        
        # 提取核心内容并生成哈希
        core_message = self.extract_log_core(message)
        log_hash = self.generate_log_hash(level, core_message, module)
        
        with self.cache_lock:
            self.stats.total_logs += 1
            
            # 检查是否为重复日志
            if log_hash in self.log_cache:
                cached_entry = self.log_cache[log_hash]
                time_diff = current_time - cached_entry.timestamp
                
                if time_diff < self.suppress_duration:
                    # 在抑制时间内，增加计数但不记录
                    cached_entry.count += 1
                    self.suppressed_counts[log_hash] += 1
                    self.stats.suppressed_logs += 1
                    self.stats.duplicated_logs += 1
                    
                    return False, f"(重复 {cached_entry.count} 次)"
                else:
                    # 超过抑制时间，更新时间戳并记录
                    suppressed_count = self.suppressed_counts.get(log_hash, 0)
                    cached_entry.timestamp = current_time
                    cached_entry.count = 1
                    self.suppressed_counts[log_hash] = 0
                    
                    additional_info = f"(之前抑制了 {suppressed_count} 次)" if suppressed_count > 0 else None
                    return True, additional_info
            else:
                # 新日志，添加到缓存
                log_entry = LogEntry(
                    timestamp=current_time,
                    level=level,
                    message=message,
                    module=module,
                    core_hash=log_hash
                )
                
                self.log_cache[log_hash] = log_entry
                self.recent_logs.append(log_entry)
                self.stats.unique_logs += 1
                
                return True, None
    
    def _cleanup_expired_logs(self, current_time: float):
        """清理过期的日志缓存"""
        with self.cache_lock:
            expired_hashes = []
            
            for log_hash, entry in self.log_cache.items():
                if current_time - entry.timestamp > self.suppress_duration * 2:
                    expired_hashes.append(log_hash)
            
            for log_hash in expired_hashes:
                del self.log_cache[log_hash]
                if log_hash in self.suppressed_counts:
                    del self.suppressed_counts[log_hash]
            
            self.last_cleanup = current_time
            
            if expired_hashes:
                print(f"🧹 清理了 {len(expired_hashes)} 个过期日志缓存项")
    
    def get_statistics(self) -> DeduplicationStats:
        """获取去重统计信息"""
        with self.cache_lock:
            if self.stats.total_logs > 0:
                self.stats.duplicate_ratio = (self.stats.duplicated_logs / self.stats.total_logs) * 100
            return self.stats
    
    def reset_statistics(self):
        """重置统计信息"""
        with self.cache_lock:
            self.stats = DeduplicationStats()
            print("📊 日志去重统计信息已重置")
    
    def log_with_deduplication(self, logger: logging.Logger, level: str, message: str, module: str = None):
        """带去重机制的日志记录"""
        if module is None:
            module = logger.name
        
        should_log, additional_info = self.should_log_message(level, message, module)
        
        if should_log:
            final_message = f"{message} {additional_info}" if additional_info else message
            
            if level.upper() == 'ERROR':
                logger.error(final_message)
            elif level.upper() == 'WARNING':
                logger.warning(final_message)
            elif level.upper() == 'INFO':
                logger.info(final_message)
            elif level.upper() == 'DEBUG':
                logger.debug(final_message)


# 全局实例
log_dedup_manager = LogDeduplicationManager()


def get_log_dedup_manager() -> LogDeduplicationManager:
    """获取全局日志去重管理器实例"""
    return log_dedup_manager


# 便捷函数
def should_log(level: str, message: str, module: str = "unknown") -> Tuple[bool, Optional[str]]:
    """便捷的日志检查函数"""
    return log_dedup_manager.should_log_message(level, message, module)


def log_with_dedup(logger: logging.Logger, level: str, message: str, module: str = None):
    """便捷的去重日志记录函数"""
    log_dedup_manager.log_with_deduplication(logger, level, message, module)


def get_dedup_stats() -> DeduplicationStats:
    """获取去重统计信息"""
    return log_dedup_manager.get_statistics()
