2025-08-02 13:56:03,045 - main_controller - INFO - 🚀 开始初始化主控制器...
2025-08-02 13:56:03,046 - main_controller - INFO - 📋 正在初始化配置管理器...
2025-08-02 13:56:03,048 - main_controller - INFO - ✅ 配置管理器初始化完成
2025-08-02 13:56:03,049 - main_controller - INFO - 📊 正在初始化数据管理器...
2025-08-02 13:56:03,050 - main_controller - INFO - ✅ 数据管理器初始化完成
2025-08-02 13:56:03,050 - main_controller - INFO - ⏱️ 正在初始化频率错误处理器...
2025-08-02 13:56:03,051 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 鼠标基础操作已启用（视觉反馈已禁用）
2025-08-02 13:56:03,056 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 频率错误处理器初始化完成
2025-08-02 13:56:03,057 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 📋 当前黑名单中有 0 个窗口
2025-08-02 13:56:03,059 - modules.frequency_error_handler.FrequencyErrorHandler - WARNING - 🚫 过滤异常小窗口: 微信 (160x28) - 可能是残留窗口
2025-08-02 13:56:03,061 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 📊 有效微信窗口统计: 1 个
2025-08-02 13:56:03,062 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-08-02 13:56:03,064 - main_controller - INFO - ✅ 频率错误处理器初始化完成
2025-08-02 13:56:03,064 - main_controller - INFO - 🪟 正在初始化窗口管理器...
2025-08-02 13:56:03,065 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-08-02 13:56:03,065 - main_controller - INFO - ✅ 窗口管理器初始化完成
2025-08-02 13:56:03,066 - main_controller - INFO - 🖥️ 正在初始化主界面管理器...
2025-08-02 13:56:03,067 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-08-02 13:56:03,073 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-08-02 13:56:03,074 - main_controller - INFO - ✅ 主界面管理器初始化完成
2025-08-02 13:56:03,076 - main_controller - INFO - 👥 正在初始化自动添加好友模块...
2025-08-02 13:56:03,078 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-02 13:56:03,085 - main_controller - INFO - ✅ 自动添加好友模块初始化完成
2025-08-02 13:56:03,087 - main_controller - INFO - 📝 正在初始化好友申请处理器...
2025-08-02 13:56:03,088 - modules.friend_request_window - INFO - ✅ 已加载配置文件: config.json
2025-08-02 13:56:03,088 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-08-02 13:56:03,088 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-08-02 13:56:03,089 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-08-02 13:56:03,089 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-08-02 13:56:03,090 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-08-02 13:56:03,090 - main_controller - INFO - ✅ 好友申请处理器初始化完成
2025-08-02 13:56:03,092 - main_controller - INFO - 🔗 正在建立组件间引用关系...
2025-08-02 13:56:03,093 - modules.window_manager - INFO - ✅ 已设置频率错误处理器引用
2025-08-02 13:56:03,093 - main_controller - INFO - ✅ 组件间引用关系建立完成
2025-08-02 13:56:03,093 - main_controller - INFO - 🧠 正在启用智能检测功能...
2025-08-02 13:56:03,094 - main_controller - INFO - 🧠 启用智能检测功能...
2025-08-02 13:56:03,094 - main_controller - INFO - ✅ 智能检测功能已启用
2025-08-02 13:56:03,094 - main_controller - INFO - 🔍 支持的检测方法:
2025-08-02 13:56:03,094 - main_controller - INFO -    1. 通过子控件检测确定按钮
2025-08-02 13:56:03,094 - main_controller - INFO -    2. 通过文本识别检测确定按钮
2025-08-02 13:56:03,095 - main_controller - INFO -    3. 通过图像识别检测确定按钮
2025-08-02 13:56:03,095 - main_controller - INFO -    4. 基于相对位置计算确定按钮坐标
2025-08-02 13:56:03,095 - main_controller - INFO - 🖱️ 支持的点击方法:
2025-08-02 13:56:03,095 - main_controller - INFO -    1. 使用Win32 API点击
2025-08-02 13:56:03,098 - main_controller - INFO -    2. 使用PyAutoGUI点击
2025-08-02 13:56:03,099 - main_controller - INFO -    3. 使用键盘Enter键
2025-08-02 13:56:03,100 - main_controller - INFO -    4. 发送窗口消息
2025-08-02 13:56:03,101 - main_controller - INFO - 🔄 重试设置: 检测最多3次, 点击最多4次
2025-08-02 13:56:03,101 - main_controller - INFO - 📱 已启用跨分辨率兼容性
2025-08-02 13:56:03,101 - main_controller - INFO - 🪟 已启用窗口位置无关性
2025-08-02 13:56:03,102 - main_controller - INFO - ✅ 智能检测功能启用完成
2025-08-02 13:56:03,102 - main_controller - INFO - ⚙️ 正在初始化执行状态...
2025-08-02 13:56:03,102 - main_controller - INFO - ✅ 执行状态初始化完成
2025-08-02 13:56:03,103 - main_controller - INFO - 📞 正在初始化GUI回调函数...
2025-08-02 13:56:03,103 - main_controller - INFO - ✅ GUI回调函数初始化完成
2025-08-02 13:56:03,103 - main_controller - INFO - 📊 正在初始化执行统计...
2025-08-02 13:56:03,104 - main_controller - INFO - ✅ 执行统计初始化完成
2025-08-02 13:56:03,104 - main_controller - INFO - 🎉 主控制器初始化完全完成！
2025-08-02 13:56:03,105 - main_controller - INFO - ✅ 微信自动化主控制器初始化完成
2025-08-02 13:56:03,105 - main_controller - INFO - 📅 当前北京时间: 2025-08-02 21:56:03
2025-08-02 13:56:03,106 - main_controller - INFO - ⏰ 时间段配置:
2025-08-02 13:56:03,106 - main_controller - INFO -    🌅 上午时段: 08:00 - 12:00 (启用: True)
2025-08-02 13:56:03,106 - main_controller - INFO -    🌇 下午时段: 14:00 - 23:59 (启用: True)
2025-08-02 13:56:03,107 - main_controller - INFO - 🕐 正在检查时间权限 - 当前北京时间: 21:56
2025-08-02 13:56:03,107 - main_controller - INFO - 📋 时间段配置检查:
2025-08-02 13:56:03,107 - main_controller - INFO -    🌅 上午时段: 08:00 - 12:00 (启用: True)
2025-08-02 13:56:03,108 - main_controller - INFO -    🌇 下午时段: 14:00 - 23:59 (启用: True)
2025-08-02 13:56:03,108 - main_controller - INFO - ⏰ 当前时间 21:56 不在上午时段 08:00-12:00 内
2025-08-02 13:56:03,108 - main_controller - INFO - ✅ 时间验证通过 - 当前时间 21:56 在下午时段 14:00-23:59 内
2025-08-02 13:56:03,109 - main_controller - INFO - ✅ 当前时间在允许的执行时间段内
