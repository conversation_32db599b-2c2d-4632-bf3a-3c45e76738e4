2025-08-04 07:48:33,324 - __main__ - INFO - 🚀 开始初始化主控制器...
2025-08-04 07:48:33,325 - __main__ - INFO - 📋 正在初始化配置管理器...
2025-08-04 07:48:33,327 - __main__ - INFO - ✅ 配置管理器初始化完成
2025-08-04 07:48:33,327 - __main__ - INFO - 📊 正在初始化数据管理器...
2025-08-04 07:48:33,347 - __main__ - INFO - ✅ 数据管理器初始化完成
2025-08-04 07:48:33,348 - __main__ - INFO - ⏱️ 正在初始化频率错误处理器...
2025-08-04 07:48:33,382 - __main__ - INFO - ✅ 频率错误处理器初始化完成
2025-08-04 07:48:33,387 - __main__ - INFO - 🪟 正在初始化窗口管理器...
2025-08-04 07:48:33,390 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-08-04 07:48:33,391 - __main__ - INFO - ✅ 窗口管理器初始化完成
2025-08-04 07:48:33,397 - __main__ - INFO - 🖥️ 正在初始化主界面管理器...
2025-08-04 07:48:33,404 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-08-04 07:48:33,407 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-08-04 07:48:33,412 - __main__ - INFO - ✅ 主界面管理器初始化完成
2025-08-04 07:48:33,414 - __main__ - INFO - 👥 正在初始化自动添加好友模块...
2025-08-04 07:48:33,431 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-04 07:48:33,480 - __main__ - INFO - ✅ 自动添加好友模块初始化完成
2025-08-04 07:48:33,493 - __main__ - INFO - 📝 正在初始化好友申请处理器...
2025-08-04 07:48:33,524 - modules.friend_request_window - INFO - [OK] 已加载配置文件: config.json
2025-08-04 07:48:33,611 - modules.friend_request_window - INFO - [TOOL] 使用固定坐标配置:
2025-08-04 07:48:33,650 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-08-04 07:48:33,692 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-08-04 07:48:33,720 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-08-04 07:48:33,784 - modules.friend_request_window - INFO - [OK] 微信好友申请窗口处理器初始化完成
2025-08-04 07:48:33,826 - __main__ - INFO - ✅ 好友申请处理器初始化完成
2025-08-04 07:48:33,850 - __main__ - INFO - 🔗 正在建立组件间引用关系...
2025-08-04 07:48:33,880 - modules.window_manager - INFO - ✅ 已设置频率错误处理器引用
2025-08-04 07:48:33,908 - __main__ - INFO - ✅ 组件间引用关系建立完成
2025-08-04 07:48:33,943 - __main__ - INFO - 🧠 正在启用智能检测功能...
2025-08-04 07:48:33,958 - __main__ - INFO - 🧠 启用智能检测功能...
2025-08-04 07:48:33,977 - __main__ - INFO - ✅ 智能检测功能已启用
2025-08-04 07:48:33,983 - __main__ - INFO - 🔍 支持的检测方法:
2025-08-04 07:48:34,025 - __main__ - INFO -    1. 通过子控件检测确定按钮
2025-08-04 07:48:34,049 - __main__ - INFO -    2. 通过文本识别检测确定按钮
2025-08-04 07:48:34,068 - __main__ - INFO -    3. 通过图像识别检测确定按钮
2025-08-04 07:48:34,087 - __main__ - INFO -    4. 基于相对位置计算确定按钮坐标
2025-08-04 07:48:34,108 - __main__ - INFO - 🖱️ 支持的点击方法:
2025-08-04 07:48:34,115 - __main__ - INFO -    1. 使用Win32 API点击
2025-08-04 07:48:34,175 - __main__ - INFO -    2. 使用PyAutoGUI点击
2025-08-04 07:48:34,214 - __main__ - INFO -    3. 使用键盘Enter键
2025-08-04 07:48:34,246 - __main__ - INFO -    4. 发送窗口消息
2025-08-04 07:48:34,299 - __main__ - INFO - 🔄 重试设置: 检测最多3次, 点击最多4次
2025-08-04 07:48:34,373 - __main__ - INFO - 📱 已启用跨分辨率兼容性
2025-08-04 07:48:34,438 - __main__ - INFO - 🪟 已启用窗口位置无关性
2025-08-04 07:48:34,446 - __main__ - INFO - ✅ 智能检测功能启用完成
2025-08-04 07:48:34,461 - __main__ - INFO - ⚙️ 正在初始化执行状态...
2025-08-04 07:48:34,481 - __main__ - INFO - ✅ 执行状态初始化完成
2025-08-04 07:48:34,483 - __main__ - INFO - 📞 正在初始化GUI回调函数...
2025-08-04 07:48:34,510 - __main__ - INFO - ✅ GUI回调函数初始化完成
2025-08-04 07:48:34,542 - __main__ - INFO - 📊 正在初始化执行统计...
2025-08-04 07:48:34,548 - __main__ - INFO - ✅ 执行统计初始化完成
2025-08-04 07:48:34,551 - __main__ - INFO - 🎉 主控制器初始化完全完成！
2025-08-04 07:48:34,571 - __main__ - INFO - ✅ 微信自动化主控制器初始化完成
2025-08-04 07:48:34,581 - __main__ - INFO - 📅 当前北京时间: 2025-08-04 15:48:34
2025-08-04 07:48:34,584 - __main__ - INFO - ⏰ 时间段配置:
2025-08-04 07:48:34,597 - __main__ - INFO -    🌅 上午时段: 08:00 - 12:00 (启用: True)
2025-08-04 07:48:34,610 - __main__ - INFO -    🌇 下午时段: 14:00 - 23:59 (启用: True)
2025-08-04 07:48:34,614 - __main__ - INFO - 🕐 正在检查时间权限 - 当前北京时间: 15:48
2025-08-04 07:48:34,615 - __main__ - INFO - 📋 时间段配置检查:
2025-08-04 07:48:34,616 - __main__ - INFO -    🌅 上午时段: 08:00 - 12:00 (启用: True)
2025-08-04 07:48:34,617 - __main__ - INFO -    🌇 下午时段: 14:00 - 23:59 (启用: True)
2025-08-04 07:48:34,620 - __main__ - INFO - ⏰ 当前时间 15:48 不在上午时段 08:00-12:00 内
2025-08-04 07:48:34,622 - __main__ - INFO - ✅ 时间验证通过 - 当前时间 15:48 在下午时段 14:00-23:59 内
2025-08-04 07:48:34,623 - __main__ - INFO - ✅ 当前时间在允许的执行时间段内
2025-08-04 07:48:34,633 - __main__ - INFO - 🚀 微信自动化添加好友主控制程序启动
2025-08-04 07:48:34,636 - __main__ - INFO - 📅 启动时间: 2025-08-04 15:48:34
2025-08-04 07:48:34,641 - __main__ - INFO - ⏰ 执行启动前时间段验证...
2025-08-04 07:48:34,643 - __main__ - INFO - 🕐 正在检查时间权限 - 当前北京时间: 15:48
2025-08-04 07:48:34,644 - __main__ - INFO - 📋 时间段配置检查:
2025-08-04 07:48:34,646 - __main__ - INFO -    🌅 上午时段: 08:00 - 12:00 (启用: True)
2025-08-04 07:48:34,647 - __main__ - INFO -    🌇 下午时段: 14:00 - 23:59 (启用: True)
2025-08-04 07:48:34,648 - __main__ - INFO - ⏰ 当前时间 15:48 不在上午时段 08:00-12:00 内
2025-08-04 07:48:34,650 - __main__ - INFO - ✅ 时间验证通过 - 当前时间 15:48 在下午时段 14:00-23:59 内
2025-08-04 07:48:34,651 - __main__ - INFO - ✅ 时间段验证通过，程序继续执行
2025-08-04 07:48:34,656 - __main__ - INFO - 🔍 开始扫描微信窗口...
2025-08-04 07:48:34,657 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-08-04 07:48:35,259 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-08-04 07:48:35,266 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 12062182, 进程: Weixin.exe)
2025-08-04 07:48:35,844 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-08-04 07:48:35,844 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 13110016, 进程: Weixin.exe)
2025-08-04 07:48:36,379 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-08-04 07:48:36,379 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 19139368, 进程: Weixin.exe)
2025-08-04 07:48:36,380 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-08-04 07:48:36,381 - __main__ - INFO - 📊 窗口扫描统计:
2025-08-04 07:48:36,381 - __main__ - INFO -   🔍 总发现窗口: 3
2025-08-04 07:48:36,382 - __main__ - INFO -   ✅ 有效可用窗口: 3
2025-08-04 07:48:36,382 - __main__ - INFO -   🚫 黑名单窗口: 0
2025-08-04 07:48:36,383 - __main__ - INFO -   👻 隐藏窗口: 0
2025-08-04 07:48:36,383 - __main__ - INFO -   ❌ 无效窗口: 0
2025-08-04 07:48:36,384 - __main__ - INFO - ✅ 最终可用微信窗口列表:
2025-08-04 07:48:36,384 - __main__ - INFO -   🔥 窗口 1: 微信 (句柄: 12062182)
2025-08-04 07:48:36,386 - __main__ - INFO -   🔥 窗口 2: 微信 (句柄: 13110016)
2025-08-04 07:48:36,388 - __main__ - INFO -   🔥 窗口 3: 微信 (句柄: 19139368)
2025-08-04 07:48:36,389 - __main__ - INFO - 🎯 开始批量移动所有微信窗口到指定坐标位置...
2025-08-04 07:48:36,391 - __main__ - INFO - 📊 需要移动的窗口数量: 3
2025-08-04 07:48:36,391 - __main__ - INFO - 🔄 移动窗口 1/3: 微信 (句柄: 12062182)
2025-08-04 07:48:36,391 - __main__ - INFO - ✅ 窗口 1 移动成功
2025-08-04 07:48:36,693 - __main__ - INFO - 🔄 移动窗口 2/3: 微信 (句柄: 13110016)
2025-08-04 07:48:36,694 - __main__ - INFO - ✅ 窗口 2 移动成功
2025-08-04 07:48:37,032 - __main__ - INFO - 🔄 移动窗口 3/3: 微信 (句柄: 19139368)
2025-08-04 07:48:37,099 - __main__ - INFO - ✅ 窗口 3 移动成功
2025-08-04 07:48:37,149 - __main__ - INFO - 📊 批量窗口移动完成统计:
2025-08-04 07:48:37,151 - __main__ - INFO -   ✅ 成功移动: 3 个窗口
2025-08-04 07:48:37,155 - __main__ - INFO -   ❌ 移动失败: 0 个窗口
2025-08-04 07:48:37,156 - __main__ - INFO -   📈 成功率: 100.0%
2025-08-04 07:48:37,158 - __main__ - INFO - 🎉 批量窗口移动操作完成，所有窗口已移动到指定坐标位置
2025-08-04 07:48:37,164 - __main__ - INFO - 📂 开始加载联系人数据...
2025-08-04 07:48:38,517 - __main__ - INFO - ✅ 加载联系人数据完成
2025-08-04 07:48:38,519 - __main__ - INFO - 📊 总联系人数: 2699
2025-08-04 07:48:38,520 - __main__ - INFO - 📋 待处理联系人数: 2636
2025-08-04 07:48:38,521 - __main__ - INFO - 🔄 开始多微信窗口循环处理
2025-08-04 07:48:38,522 - __main__ - INFO - 📊 总窗口数: 3, 总联系人数: 2636
2025-08-04 07:48:38,524 - __main__ - INFO - 🕐 正在检查时间权限 - 当前北京时间: 15:48
2025-08-04 07:48:38,525 - __main__ - INFO - 📋 时间段配置检查:
2025-08-04 07:48:38,526 - __main__ - INFO -    🌅 上午时段: 08:00 - 12:00 (启用: True)
2025-08-04 07:48:38,526 - __main__ - INFO -    🌇 下午时段: 14:00 - 23:59 (启用: True)
2025-08-04 07:48:38,527 - __main__ - INFO - ⏰ 当前时间 15:48 不在上午时段 08:00-12:00 内
2025-08-04 07:48:38,527 - __main__ - INFO - ✅ 时间验证通过 - 当前时间 15:48 在下午时段 14:00-23:59 内
2025-08-04 07:48:38,529 - __main__ - INFO - 📊 初始化统计：总联系人数=2636，总窗口数=3
2025-08-04 07:48:38,534 - __main__ - INFO - 🕐 正在检查时间权限 - 当前北京时间: 15:48
2025-08-04 07:48:38,534 - __main__ - INFO - 📋 时间段配置检查:
2025-08-04 07:48:38,546 - __main__ - INFO -    🌅 上午时段: 08:00 - 12:00 (启用: True)
2025-08-04 07:48:38,563 - __main__ - INFO -    🌇 下午时段: 14:00 - 23:59 (启用: True)
2025-08-04 07:48:38,580 - __main__ - INFO - ⏰ 当前时间 15:48 不在上午时段 08:00-12:00 内
2025-08-04 07:48:38,582 - __main__ - INFO - ✅ 时间验证通过 - 当前时间 15:48 在下午时段 14:00-23:59 内
2025-08-04 07:48:38,584 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-08-04 07:48:38,614 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-08-04 07:48:38,616 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 19139368, 进程: Weixin.exe)
2025-08-04 07:48:38,617 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-08-04 07:48:38,622 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 13110016, 进程: Weixin.exe)
2025-08-04 07:48:38,637 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-08-04 07:48:38,643 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 12062182, 进程: Weixin.exe)
2025-08-04 07:48:38,651 - modules.window_manager - INFO - 🎯 总共找到 3 个微信窗口
2025-08-04 07:48:38,655 - __main__ - INFO - 📊 窗口扫描统计:
2025-08-04 07:48:38,663 - __main__ - INFO -   🔍 总发现窗口: 3
2025-08-04 07:48:38,664 - __main__ - INFO -   ✅ 有效可用窗口: 3
2025-08-04 07:48:38,666 - __main__ - INFO -   🚫 黑名单窗口: 0
2025-08-04 07:48:38,667 - __main__ - INFO -   👻 隐藏窗口: 0
2025-08-04 07:48:38,671 - __main__ - INFO -   ❌ 无效窗口: 0
2025-08-04 07:48:38,673 - __main__ - INFO - ✅ 最终可用微信窗口列表:
2025-08-04 07:48:38,680 - __main__ - INFO -   🔥 窗口 1: 微信 (句柄: 12062182)
2025-08-04 07:48:38,681 - __main__ - INFO -   🔥 窗口 2: 微信 (句柄: 13110016)
2025-08-04 07:48:38,681 - __main__ - INFO -   🔥 窗口 3: 微信 (句柄: 19139368)
2025-08-04 07:48:38,688 - __main__ - INFO - 
============================================================
2025-08-04 07:48:38,689 - __main__ - INFO - 🎯 开始处理第 1/3 个微信窗口 (第 1 轮)
2025-08-04 07:48:38,697 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 12062182)
2025-08-04 07:48:38,699 - __main__ - INFO - 📊 全局进度：已处理 0/2636 个联系人（剩余 2636 个）
2025-08-04 07:48:38,700 - __main__ - INFO - ============================================================
2025-08-04 07:48:38,700 - __main__ - INFO - 🚀 开始处理微信窗口 1
2025-08-04 07:48:38,703 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 12062182)
2025-08-04 07:48:38,715 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-08-04 07:48:38,716 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 1
2025-08-04 07:48:38,716 - __main__ - INFO - 🔍 检查目标窗口状态: 微信 (句柄: 12062182)
2025-08-04 07:48:38,717 - __main__ - INFO - 🔄 检测到其他微信窗口在前台: 微信
2025-08-04 07:48:38,717 - __main__ - INFO - ⏳ 等待前台窗口稳定后再激活目标窗口...
2025-08-04 07:48:39,718 - __main__ - INFO - 🎯 激活目标窗口: 微信
